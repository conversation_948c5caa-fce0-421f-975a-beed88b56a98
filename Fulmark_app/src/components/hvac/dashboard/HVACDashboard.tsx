'use client';

// 🌟 HVAC Dashboard - PEŁNA MOC WIATRU! ⚡
// Complete dashboard with cosmic-level UX and AI insights

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  LayoutDashboard,
  Users,
  Settings,
  Cloud,
  BarChart,
  ChevronRight,
  Wrench,
  Zap,
  Package,
  FileText,
  BarChart2,
  Activity,
  User,
  Shield,
  GraduationCap,
  ClipboardList,
  Briefcase,
  Gauge,
  Calendar
} from 'lucide-react';
import BusinessIntelligence from './BusinessIntelligence';
import WeatherIntegration from './WeatherIntegration';
import PredictiveMaintenance from './PredictiveMaintenance';
import CustomerAnalytics from './CustomerAnalytics';
import ServiceManagement from './ServiceManagement';
import EnergyOptimization from './EnergyOptimization';
import SystemMonitoring from './SystemMonitoring';
import SettingsConfiguration from './SettingsConfiguration';
import UserManagement from './UserManagement';
import ProjectManagement from './ProjectManagement';
import ComplianceCertification from './ComplianceCertification';
import TrainingDevelopment from './TrainingDevelopment';
import SystemPerformance from './SystemPerformance';
import { CalendarEventsView } from '../../calendar/CalendarEventsView';
import { CalendarConnectionManager } from '../../calendar/CalendarConnectionManager';
import { CalendarSyncManager } from '../../calendar/CalendarSyncManager';
import { CalendarOverview } from '../../calendar/CalendarOverview';

const HVACDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data for Business Intelligence
  const businessIntelligenceData = {
    revenue: {
      current: 150000,
      growth: 25,
      history: [
        { date: '2024-01', amount: 120000 },
        { date: '2024-02', amount: 130000 },
        { date: '2024-03', amount: 140000 },
        { date: '2024-04', amount: 150000 }
      ]
    },
    profit: {
      current: 45000,
      margin: 30,
      history: [
        { date: '2024-01', amount: 36000 },
        { date: '2024-02', amount: 39000 },
        { date: '2024-03', amount: 42000 },
        { date: '2024-04', amount: 45000 }
      ]
    },
    customers: {
      total: 250,
      retention: 85,
      segments: [
        { name: 'Residential', value: 150 },
        { name: 'Commercial', value: 80 },
        { name: 'Industrial', value: 20 }
      ]
    },
    efficiency: {
      utilization: 85,
      responseTime: 90,
      metrics: [
        { name: 'Energy Efficiency', value: 85 },
        { name: 'Operational Efficiency', value: 90 },
        { name: 'Maintenance Efficiency', value: 88 }
      ]
    }
  };

  // Mock data for Weather Integration
  const weatherData = {
    current: {
      temperature: 22,
      feelsLike: 24,
      humidity: 65,
      windSpeed: 15,
      windDirection: 'NE',
      condition: 'sunny',
      icon: 'sun'
    },
    forecast: [
      {
        date: '2024-04-01',
        temperature: { min: 18, max: 25 },
        condition: 'sunny',
        icon: 'sun',
        precipitation: 10,
        windSpeed: 12
      },
      {
        date: '2024-04-02',
        temperature: { min: 17, max: 23 },
        condition: 'cloudy',
        icon: 'cloud',
        precipitation: 30,
        windSpeed: 18
      },
      {
        date: '2024-04-03',
        temperature: { min: 16, max: 22 },
        condition: 'rain',
        icon: 'rain',
        precipitation: 60,
        windSpeed: 20
      }
    ],
    alerts: [
      {
        type: 'warning' as const,
        message: 'Silne wiatry przewidywane w ciągu najbliższych 24 godzin',
        severity: 'medium' as const
      }
    ],
    hvacRecommendations: [
      {
        type: 'cooling' as const,
        recommendation: 'Zwiększ wydajność chłodzenia w godzinach szczytu',
        impact: 'high' as const
      },
      {
        type: 'ventilation' as const,
        recommendation: 'Dostosuj wentylację do warunków wietrznych',
        impact: 'medium' as const
      }
    ]
  };

  // Mock data for Predictive Maintenance
  const maintenanceData = {
    systemHealth: {
      overall: 92,
      components: [
        {
          name: 'Sprężarka',
          health: 95,
          status: 'optimal' as const,
          lastMaintenance: '2024-03-15',
          nextMaintenance: '2024-06-15'
        },
        {
          name: 'Wentylator',
          health: 88,
          status: 'warning' as const,
          lastMaintenance: '2024-02-20',
          nextMaintenance: '2024-05-20'
        },
        {
          name: 'Filtry',
          health: 75,
          status: 'critical' as const,
          lastMaintenance: '2024-01-10',
          nextMaintenance: '2024-04-10'
        }
      ]
    },
    predictiveAlerts: [
      {
        id: '1',
        component: 'Filtry',
        type: 'replacement' as const,
        severity: 'high' as const,
        predictedDate: '2024-04-15',
        description: 'Wymagana wymiana filtrów',
        impact: 'Wysoka - może wpłynąć na wydajność systemu'
      }
    ],
    maintenanceHistory: [
      {
        id: '1',
        date: '2024-03-15',
        type: 'Konserwacja rutynowa',
        description: 'Przegląd i czyszczenie sprężarki',
        technician: 'Jan Kowalski',
        cost: 1200,
        status: 'completed' as const
      }
    ],
    efficiencyMetrics: {
      energyEfficiency: 88,
      operationalEfficiency: 92,
      maintenanceEfficiency: 90,
      costSavings: 15000
    }
  };

  // Mock data for Customer Analytics
  const customerData = {
    overview: {
      totalCustomers: 250,
      activeCustomers: 200,
      newCustomers: 30,
      churnRate: 5
    },
    satisfaction: {
      averageRating: 4.5,
      ratingDistribution: [
        { rating: 5, count: 120 },
        { rating: 4, count: 80 },
        { rating: 3, count: 30 },
        { rating: 2, count: 15 },
        { rating: 1, count: 5 }
      ],
      recentReviews: [
        {
          id: '1',
          customer: 'Anna Nowak',
          rating: 5,
          comment: 'Świetna obsługa i profesjonalne podejście',
          date: '2024-03-28'
        }
      ]
    },
    journey: {
      stages: [
        { name: 'Kontakt', count: 50, conversionRate: 100 },
        { name: 'Konsultacja', count: 40, conversionRate: 80 },
        { name: 'Oferta', count: 35, conversionRate: 70 },
        { name: 'Realizacja', count: 30, conversionRate: 60 }
      ],
      recentActivities: [
        {
          id: '1',
          customer: 'Jan Kowalski',
          action: 'Złożono zapytanie o ofertę',
          stage: 'Kontakt',
          date: '2024-03-29'
        }
      ]
    },
    revenue: {
      totalRevenue: 150000,
      averageRevenue: 600,
      revenueByService: [
        { service: 'Instalacja', amount: 60000, percentage: 40 },
        { service: 'Konserwacja', amount: 45000, percentage: 30 },
        { service: 'Naprawy', amount: 30000, percentage: 20 },
        { service: 'Konsultacje', amount: 15000, percentage: 10 }
      ],
      monthlyTrend: [
        { month: 'Sty', revenue: 120000, customers: 200 },
        { month: 'Lut', revenue: 130000, customers: 210 },
        { month: 'Mar', revenue: 140000, customers: 220 },
        { month: 'Kwi', revenue: 150000, customers: 250 }
      ]
    }
  };

  // Mock data for Service Management
  const serviceData = {
    tickets: [
      {
        id: 'T001',
        customer: 'Jan Kowalski',
        type: 'maintenance' as const,
        status: 'in-progress' as const,
        priority: 'high' as const,
        assignedTo: 'Adam Nowak',
        scheduledDate: '2024-04-01',
        location: 'Warszawa',
        description: 'Konserwacja klimatyzacji'
      },
      {
        id: 'T002',
        customer: 'Anna Wiśniewska',
        type: 'repair' as const,
        status: 'pending' as const,
        priority: 'urgent' as const,
        assignedTo: 'Piotr Kowalczyk',
        scheduledDate: '2024-04-02',
        location: 'Kraków',
        description: 'Awaria ogrzewania'
      }
    ],
    technicians: [
      {
        id: 'T001',
        name: 'Adam Nowak',
        specialization: ['HVAC', 'Klimatyzacja'],
        status: 'busy' as const,
        currentLocation: 'Warszawa',
        rating: 4.8,
        completedJobs: 150
      },
      {
        id: 'T002',
        name: 'Piotr Kowalczyk',
        specialization: ['Ogrzewanie', 'Wentylacja'],
        status: 'available' as const,
        currentLocation: 'Kraków',
        rating: 4.9,
        completedJobs: 200
      }
    ],
    metrics: {
      totalTickets: 50,
      openTickets: 15,
      completedTickets: 35,
      averageResolutionTime: 4.5,
      customerSatisfaction: 95,
      ticketDistribution: [
        { type: 'Instalacja', count: 20 },
        { type: 'Konserwacja', count: 15 },
        { type: 'Naprawa', count: 10 },
        { type: 'Inspekcja', count: 5 }
      ],
      priorityDistribution: [
        { priority: 'Niski', count: 15 },
        { priority: 'Średni', count: 20 },
        { priority: 'Wysoki', count: 10 },
        { priority: 'Pilny', count: 5 }
      ]
    }
  };

  // Mock data for Energy Optimization
  const energyData = {
    consumption: [
      { timestamp: '2024-03-25', value: 1200, cost: 600, type: 'total' as const },
      { timestamp: '2024-03-26', value: 1150, cost: 575, type: 'total' as const },
      { timestamp: '2024-03-27', value: 1300, cost: 650, type: 'total' as const },
      { timestamp: '2024-03-28', value: 1250, cost: 625, type: 'total' as const }
    ],
    recommendations: [
      {
        id: 'R001',
        type: 'energy' as const,
        priority: 'high' as const,
        title: 'Optymalizacja wentylacji',
        description: 'Dostosowanie wentylacji do aktualnych warunków pogodowych',
        potentialSavings: 5000,
        implementationCost: 2000,
        paybackPeriod: 5
      },
      {
        id: 'R002',
        type: 'efficiency' as const,
        priority: 'medium' as const,
        title: 'Modernizacja sterowania',
        description: 'Wdrożenie inteligentnego systemu sterowania HVAC',
        potentialSavings: 8000,
        implementationCost: 15000,
        paybackPeriod: 23
      }
    ],
    metrics: {
      currentConsumption: 1250,
      averageConsumption: 1225,
      peakConsumption: 1500,
      costSavings: 15000,
      efficiencyScore: 88,
      carbonReduction: 2500
    }
  };

  // Add mock data for System Monitoring
  const systemMonitoringData = {
    metrics: [
      {
        id: '1',
        name: 'CPU Usage',
        value: 45,
        unit: '%',
        status: 'normal' as const,
        trend: 'stable' as const,
        threshold: {
          warning: 70,
          critical: 90,
        },
      },
      {
        id: '2',
        name: 'Memory Usage',
        value: 60,
        unit: '%',
        status: 'warning' as const,
        trend: 'up' as const,
        threshold: {
          warning: 65,
          critical: 85,
        },
      },
      {
        id: '3',
        name: 'Network Load',
        value: 30,
        unit: '%',
        status: 'normal' as const,
        trend: 'down' as const,
        threshold: {
          warning: 75,
          critical: 90,
        },
      },
      {
        id: '4',
        name: 'Storage Usage',
        value: 75,
        unit: '%',
        status: 'warning' as const,
        trend: 'up' as const,
        threshold: {
          warning: 70,
          critical: 85,
        },
      },
    ],
    alerts: [
      {
        id: '1',
        type: 'warning' as const,
        message: 'High memory usage detected',
        timestamp: '2024-02-20 10:30:00',
        source: 'System Monitor',
        status: 'active' as const,
        priority: 'medium' as const,
      },
      {
        id: '2',
        type: 'error' as const,
        message: 'Database connection timeout',
        timestamp: '2024-02-20 10:15:00',
        source: 'Database Service',
        status: 'active' as const,
        priority: 'high' as const,
      },
      {
        id: '3',
        type: 'info' as const,
        message: 'System backup completed',
        timestamp: '2024-02-20 09:00:00',
        source: 'Backup Service',
        status: 'resolved' as const,
        priority: 'low' as const,
      },
    ],
    status: {
      overall: 'degraded' as const,
      components: [
        {
          name: 'Database Server',
          status: 'operational' as const,
          lastChecked: '2024-02-20 10:30:00',
        },
        {
          name: 'Application Server',
          status: 'degraded' as const,
          lastChecked: '2024-02-20 10:30:00',
        },
        {
          name: 'File Storage',
          status: 'degraded' as const,
          lastChecked: '2024-02-20 10:30:00',
        },
        {
          name: 'Network Services',
          status: 'operational' as const,
          lastChecked: '2024-02-20 10:30:00',
        },
      ],
    },
  };

  // Add mock data for Settings & Configuration
  const settingsData = {
    notifications: [
      {
        id: '1',
        type: 'email' as const,
        name: 'Email Notifications',
        description: 'Receive notifications via email',
        enabled: true,
      },
      {
        id: '2',
        type: 'push' as const,
        name: 'Push Notifications',
        description: 'Receive push notifications on your device',
        enabled: true,
      },
      {
        id: '3',
        type: 'sms' as const,
        name: 'SMS Notifications',
        description: 'Receive notifications via SMS',
        enabled: false,
      },
    ],
    security: [
      {
        id: '1',
        name: 'Password',
        description: 'Change your account password',
        value: '',
        type: 'password' as const,
      },
      {
        id: '2',
        name: 'Two-Factor Authentication',
        description: 'Enable 2FA for additional security',
        value: '',
        type: '2fa' as const,
      },
      {
        id: '3',
        name: 'Active Sessions',
        description: 'Manage your active sessions',
        value: '',
        type: 'session' as const,
      },
    ],
    preferences: [
      {
        id: '1',
        name: 'Theme',
        description: 'Choose your preferred theme',
        value: 'light',
        type: 'theme' as const,
      },
      {
        id: '2',
        name: 'Language',
        description: 'Select your preferred language',
        value: 'en',
        type: 'language' as const,
      },
      {
        id: '3',
        name: 'Timezone',
        description: 'Set your local timezone',
        value: 'CET',
        type: 'timezone' as const,
      },
    ],
  };

  // Add mock data for User Management
  const userManagementData = {
    users: [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+48 ***********',
        role: 'Administrator',
        status: 'active' as const,
        lastLogin: '2024-02-20 10:30:00',
        company: 'HVAC Solutions',
        department: 'IT',
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+48 ***********',
        role: 'Technician',
        status: 'active' as const,
        lastLogin: '2024-02-20 09:15:00',
        company: 'HVAC Solutions',
        department: 'Service',
      },
      {
        id: '3',
        name: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '+48 ***********',
        role: 'Manager',
        status: 'inactive' as const,
        lastLogin: '2024-02-19 16:45:00',
        company: 'HVAC Solutions',
        department: 'Operations',
      },
    ],
    roles: [
      {
        id: '1',
        name: 'Administrator',
        description: 'Full system access and control',
        permissions: [
          'Manage Users',
          'Manage Roles',
          'System Configuration',
          'View Reports',
          'Manage Settings',
        ],
      },
      {
        id: '2',
        name: 'Technician',
        description: 'Access to service and maintenance features',
        permissions: [
          'View Service Tickets',
          'Update Service Status',
          'Access Maintenance Tools',
          'View Customer Data',
        ],
      },
      {
        id: '3',
        name: 'Manager',
        description: 'Access to management and reporting features',
        permissions: [
          'View Reports',
          'Manage Service Teams',
          'Access Analytics',
          'View Customer Data',
        ],
      },
    ],
  };

  // Add mock data for Project Management
  const projectManagementData = {
    projects: [
      {
        id: '1',
        name: 'HVAC System Upgrade',
        description: 'Upgrade of the main HVAC system in Building A',
        status: 'in-progress' as const,
        startDate: '2024-02-01',
        endDate: '2024-03-15',
        progress: 65,
        budget: 150000,
        location: 'Building A, Floor 3',
        team: [
          {
            id: '1',
            name: 'John Doe',
            role: 'Project Manager',
          },
          {
            id: '2',
            name: 'Jane Smith',
            role: 'HVAC Technician',
          },
          {
            id: '3',
            name: 'Mike Johnson',
            role: 'Electrician',
          },
        ],
      },
      {
        id: '2',
        name: 'Maintenance Schedule',
        description: 'Regular maintenance of all HVAC units',
        status: 'planning' as const,
        startDate: '2024-03-01',
        endDate: '2024-03-31',
        progress: 20,
        budget: 50000,
        location: 'All Buildings',
        team: [
          {
            id: '4',
            name: 'Sarah Wilson',
            role: 'Maintenance Supervisor',
          },
          {
            id: '5',
            name: 'Tom Brown',
            role: 'HVAC Technician',
          },
        ],
      },
      {
        id: '3',
        name: 'Energy Efficiency Audit',
        description: 'Comprehensive audit of HVAC energy consumption',
        status: 'completed' as const,
        startDate: '2024-01-15',
        endDate: '2024-02-15',
        progress: 100,
        budget: 30000,
        location: 'Headquarters',
        team: [
          {
            id: '6',
            name: 'Lisa Chen',
            role: 'Energy Consultant',
          },
          {
            id: '7',
            name: 'David Miller',
            role: 'HVAC Engineer',
          },
        ],
      },
    ],
    tasks: [
      {
        id: '1',
        projectId: '1',
        name: 'Install New Compressor',
        description: 'Replace the main compressor unit',
        status: 'in-progress' as const,
        priority: 'high' as const,
        assignedTo: 'Jane Smith',
        dueDate: '2024-02-25',
        dependencies: [],
      },
      {
        id: '2',
        projectId: '1',
        name: 'Update Control System',
        description: 'Install new control system software',
        status: 'todo' as const,
        priority: 'medium' as const,
        assignedTo: 'Mike Johnson',
        dueDate: '2024-03-01',
        dependencies: ['1'],
      },
      {
        id: '3',
        projectId: '2',
        name: 'Create Maintenance Schedule',
        description: 'Develop detailed maintenance schedule',
        status: 'completed' as const,
        priority: 'high' as const,
        assignedTo: 'Sarah Wilson',
        dueDate: '2024-02-28',
        dependencies: [],
      },
    ],
    resources: [
      {
        id: '1',
        name: 'HVAC Compressor',
        type: 'equipment' as const,
        status: 'allocated' as const,
        quantity: 1,
        unit: 'unit',
        cost: 25000,
        projectId: '1',
      },
      {
        id: '2',
        name: 'Control System Software',
        type: 'material' as const,
        status: 'available' as const,
        quantity: 1,
        unit: 'license',
        cost: 5000,
        projectId: '1',
      },
      {
        id: '3',
        name: 'HVAC Technicians',
        type: 'labor' as const,
        status: 'allocated' as const,
        quantity: 3,
        unit: 'person',
        cost: 150,
        projectId: '2',
      },
    ],
  };

  // Add mock data for Compliance & Certification
  const complianceData = {
    certifications: [
      {
        id: '1',
        name: 'ISO 9001:2015',
        type: 'quality' as const,
        status: 'active' as const,
        issueDate: '2023-01-15',
        expiryDate: '2026-01-15',
        issuer: 'ISO Certification Body',
        requirements: [
          'Quality Management System',
          'Document Control',
          'Process Management',
        ],
        documents: [
          {
            id: '1',
            name: 'Certificate',
            type: 'PDF',
            url: '#',
          },
          {
            id: '2',
            name: 'Audit Report',
            type: 'PDF',
            url: '#',
          },
        ],
      },
      {
        id: '2',
        name: 'Safety Certification',
        type: 'safety' as const,
        status: 'active' as const,
        issueDate: '2023-03-20',
        expiryDate: '2024-03-20',
        issuer: 'Safety Standards Board',
        requirements: [
          'Safety Procedures',
          'Equipment Maintenance',
          'Staff Training',
        ],
        documents: [
          {
            id: '1',
            name: 'Certificate',
            type: 'PDF',
            url: '#',
          },
        ],
      },
    ],
    requirements: [
      {
        id: '1',
        name: 'Environmental Compliance',
        category: 'environmental' as const,
        status: 'compliant' as const,
        lastChecked: '2024-01-15',
        nextCheck: '2024-07-15',
        description: 'Environmental protection and waste management compliance',
        regulations: ['EPA Standards', 'Local Regulations'],
        evidence: [
          {
            id: '1',
            name: 'Compliance Report',
            type: 'PDF',
            url: '#',
          },
        ],
      },
      {
        id: '2',
        name: 'Safety Standards',
        category: 'safety' as const,
        status: 'compliant' as const,
        lastChecked: '2024-02-01',
        nextCheck: '2024-08-01',
        description: 'Workplace safety and equipment standards',
        regulations: ['OSHA Standards', 'Industry Guidelines'],
        evidence: [
          {
            id: '1',
            name: 'Safety Audit',
            type: 'PDF',
            url: '#',
          },
        ],
      },
    ],
    audits: [
      {
        id: '1',
        name: 'Annual Quality Audit',
        type: 'internal' as const,
        status: 'completed' as const,
        date: '2024-01-20',
        auditor: 'Internal Audit Team',
        findings: [
          {
            id: '1',
            type: 'minor' as const,
            description: 'Documentation update needed',
            status: 'open' as const,
            dueDate: '2024-03-20',
          },
          {
            id: '2',
            type: 'observation' as const,
            description: 'Process improvement opportunity',
            status: 'in-progress' as const,
            dueDate: '2024-04-20',
          },
        ],
      },
      {
        id: '2',
        name: 'Safety Compliance Audit',
        type: 'external' as const,
        status: 'scheduled' as const,
        date: '2024-04-15',
        auditor: 'External Safety Consultants',
        findings: [],
      },
    ],
  };

  // Add mock data for Training & Development
  const trainingData = {
    trainings: [
      {
        id: '1',
        title: 'HVAC System Maintenance',
        type: 'technical' as const,
        status: 'scheduled' as const,
        startDate: '2024-04-15',
        endDate: '2024-04-16',
        instructor: 'John Smith',
        location: 'Training Center A',
        participants: [
          {
            id: '1',
            name: 'Alice Johnson',
            role: 'Technician',
            status: 'registered' as const,
          },
          {
            id: '2',
            name: 'Bob Wilson',
            role: 'Technician',
            status: 'registered' as const,
          },
        ],
        materials: [
          {
            id: '1',
            name: 'Course Manual',
            type: 'PDF',
            url: '#',
          },
          {
            id: '2',
            name: 'Practice Exercises',
            type: 'PDF',
            url: '#',
          },
        ],
      },
      {
        id: '2',
        title: 'Safety Procedures',
        type: 'safety' as const,
        status: 'in-progress' as const,
        startDate: '2024-03-20',
        endDate: '2024-03-21',
        instructor: 'Sarah Davis',
        location: 'Training Center B',
        participants: [
          {
            id: '3',
            name: 'Charlie Brown',
            role: 'Technician',
            status: 'attending' as const,
          },
        ],
        materials: [
          {
            id: '1',
            name: 'Safety Guidelines',
            type: 'PDF',
            url: '#',
          },
        ],
      },
    ],
    skills: [
      {
        id: '1',
        name: 'HVAC System Installation',
        category: 'technical' as const,
        level: 'advanced' as const,
        employees: [
          {
            id: '1',
            name: 'Alice Johnson',
            level: 'advanced' as const,
            lastAssessed: '2024-02-15',
          },
          {
            id: '2',
            name: 'Bob Wilson',
            level: 'intermediate' as const,
            lastAssessed: '2024-01-20',
          },
        ],
        requiredTrainings: ['HVAC System Maintenance', 'Safety Procedures'],
      },
      {
        id: '2',
        name: 'Customer Service',
        category: 'soft-skills' as const,
        level: 'intermediate' as const,
        employees: [
          {
            id: '3',
            name: 'Charlie Brown',
            level: 'beginner' as const,
            lastAssessed: '2024-03-01',
          },
        ],
        requiredTrainings: ['Customer Communication', 'Problem Solving'],
      },
    ],
    developmentPlans: [
      {
        id: '1',
        employeeId: '1',
        employeeName: 'Alice Johnson',
        role: 'Senior Technician',
        goals: [
          {
            id: '1',
            title: 'Master Advanced HVAC Systems',
            description: 'Complete advanced training in complex HVAC systems',
            targetDate: '2024-06-30',
            status: 'in-progress' as const,
            skills: ['HVAC System Installation', 'System Troubleshooting'],
          },
        ],
        trainings: [
          {
            id: '1',
            title: 'Advanced HVAC Systems',
            status: 'scheduled' as const,
          },
        ],
        skills: [
          {
            id: '1',
            name: 'HVAC System Installation',
            currentLevel: 'advanced' as const,
            targetLevel: 'expert' as const,
            progress: 75,
          },
        ],
      },
      {
        id: '2',
        employeeId: '2',
        employeeName: 'Bob Wilson',
        role: 'Technician',
        goals: [
          {
            id: '1',
            title: 'Improve Customer Service',
            description: 'Enhance customer interaction skills',
            targetDate: '2024-05-31',
            status: 'not-started' as const,
            skills: ['Customer Service', 'Communication'],
          },
        ],
        trainings: [
          {
            id: '1',
            title: 'Customer Service Excellence',
            status: 'scheduled' as const,
          },
        ],
        skills: [
          {
            id: '1',
            name: 'Customer Service',
            currentLevel: 'beginner' as const,
            targetLevel: 'intermediate' as const,
            progress: 25,
          },
        ],
      },
    ],
  };

  // Add mock data for System Performance
  const systemPerformanceData = {
    metrics: [
      {
        id: '1',
        name: 'Temperature',
        value: 22.5,
        unit: '°C',
        status: 'normal' as const,
        trend: 'stable' as const,
        threshold: {
          warning: 25,
          critical: 28,
        },
      },
      {
        id: '2',
        name: 'Humidity',
        value: 45,
        unit: '%',
        status: 'normal' as const,
        trend: 'down' as const,
        threshold: {
          warning: 60,
          critical: 70,
        },
      },
      {
        id: '3',
        name: 'Pressure',
        value: 1013,
        unit: 'hPa',
        status: 'normal' as const,
        trend: 'stable' as const,
        threshold: {
          warning: 1020,
          critical: 1030,
        },
      },
      {
        id: '4',
        name: 'Flow Rate',
        value: 2.5,
        unit: 'm³/s',
        status: 'warning' as const,
        trend: 'up' as const,
        threshold: {
          warning: 2.0,
          critical: 3.0,
        },
      },
    ],
    performanceHistory: [
      {
        timestamp: '2024-03-20 00:00',
        temperature: 22.0,
        humidity: 48,
        pressure: 1012,
        flowRate: 2.2,
        powerConsumption: 1500,
        efficiency: 85,
      },
      {
        timestamp: '2024-03-20 01:00',
        temperature: 22.2,
        humidity: 47,
        pressure: 1013,
        flowRate: 2.3,
        powerConsumption: 1550,
        efficiency: 84,
      },
      // Add more historical data points...
    ],
    alerts: [
      {
        id: '1',
        type: 'performance' as const,
        severity: 'high' as const,
        message: 'Flow rate exceeding normal operating range',
        timestamp: '2024-03-20 02:15',
        status: 'active' as const,
        systemId: 'HVAC-001',
        systemName: 'Main HVAC System',
      },
      {
        id: '2',
        type: 'maintenance' as const,
        severity: 'medium' as const,
        message: 'Filter replacement due in 7 days',
        timestamp: '2024-03-20 01:30',
        status: 'acknowledged' as const,
        systemId: 'HVAC-002',
        systemName: 'Office HVAC System',
      },
    ],
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: LayoutDashboard },
    { id: 'analytics', label: 'Analytics', icon: BarChart },
    { id: 'calendar', label: 'Calendar', icon: Calendar },
    { id: 'maintenance', label: 'Maintenance', icon: Settings },
    { id: 'weather', label: 'Weather', icon: Cloud },
    { id: 'customers', label: 'Customers', icon: Users },
    { id: 'service', label: 'Service', icon: Wrench },
    { id: 'energy', label: 'Energy', icon: Zap },
    { id: 'inventory', label: 'Inventory', icon: Package },
    { id: 'documents', label: 'Documents', icon: FileText },
    { id: 'reports', label: 'Reports', icon: BarChart2 },
    { id: 'system', label: 'System', icon: Activity },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'users', label: 'Users', icon: User },
    { id: 'projects', label: 'Projects', icon: FileText },
    { id: 'compliance', label: 'Compliance', icon: Shield },
    { id: 'training', label: 'Training', icon: GraduationCap },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <BusinessIntelligence data={businessIntelligenceData} />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <WeatherIntegration data={weatherData} />
              <PredictiveMaintenance data={maintenanceData} />
              <CalendarOverview />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ServiceManagement
                tickets={serviceData.tickets}
                technicians={serviceData.technicians}
                metrics={serviceData.metrics}
              />
              <EnergyOptimization
                consumption={energyData.consumption}
                recommendations={energyData.recommendations}
                metrics={energyData.metrics}
              />
            </div>
          </div>
        );
      case 'analytics':
        return <BusinessIntelligence data={businessIntelligenceData} />;
      case 'calendar':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <CalendarConnectionManager />
              <CalendarSyncManager />
            </div>
            <CalendarEventsView
              showFilters={true}
              maxEvents={100}
              defaultView="month"
            />
          </div>
        );
      case 'maintenance':
        return <PredictiveMaintenance data={maintenanceData} />;
      case 'weather':
        return <WeatherIntegration data={weatherData} />;
      case 'customers':
        return <CustomerAnalytics data={customerData} />;
      case 'service':
        return (
          <ServiceManagement
            tickets={serviceData.tickets}
            technicians={serviceData.technicians}
            metrics={serviceData.metrics}
          />
        );
      case 'energy':
        return (
          <EnergyOptimization
            consumption={energyData.consumption}
            recommendations={energyData.recommendations}
            metrics={energyData.metrics}
          />
        );
      case 'system':
        return <SystemPerformance {...systemPerformanceData} />;
      case 'settings':
        return <SettingsConfiguration {...settingsData} />;
      case 'users':
        return <UserManagement {...userManagementData} />;
      case 'projects':
        return <ProjectManagement {...projectManagementData} />;
      case 'compliance':
        return <ComplianceCertification {...complianceData} />;
      case 'training':
        return <TrainingDevelopment {...trainingData} />;
      default:
        return <div>Select a tab to view content</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm">
          {/* Navigation Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                    {activeTab === tab.id && (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="p-6">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HVACDashboard;