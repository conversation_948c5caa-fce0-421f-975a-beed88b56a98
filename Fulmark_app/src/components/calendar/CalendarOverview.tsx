'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  Calendar, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Brain,
  Users,
  Target,
  Zap,
  Loader2,
  ChevronRight
} from 'lucide-react';
import { useToast } from '../ui/use-toast';

interface CalendarOverviewData {
  totalEvents: number;
  futureEvents: number;
  recentEvents: number;
  eventsByType: Record<string, number>;
  averageSentiment: number;
  avgConfidence: number;
  customerMatchRate: number;
  nextEvents: Array<{
    id: string;
    subject: string;
    startTime: string;
    eventType: string | null;
    customerId?: string;
  }>;
}

interface CalendarOverviewProps {
  className?: string;
}

export function CalendarOverview({ className }: CalendarOverviewProps) {
  const [data, setData] = useState<CalendarOverviewData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchOverviewData();
  }, []);

  const fetchOverviewData = async () => {
    try {
      setIsLoading(true);
      
      // Get events for the next 30 days
      const now = new Date();
      const futureDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      
      const params = new URLSearchParams({
        startDate: now.toISOString(),
        endDate: futureDate.toISOString(),
        limit: '100'
      });
      
      const response = await fetch(`/api/calendar/events?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch calendar overview');
      }

      const result = await response.json();
      
      // Process the data for overview
      const events = result.events || [];
      const stats = result.statistics || {};
      
      const overviewData: CalendarOverviewData = {
        totalEvents: stats.totalEvents || 0,
        futureEvents: events.length,
        recentEvents: stats.recentEvents || 0,
        eventsByType: stats.eventsByType || {},
        averageSentiment: stats.averageSentiment || 0,
        avgConfidence: stats.avgConfidence || 0,
        customerMatchRate: stats.customerMatchRate || 0,
        nextEvents: events.slice(0, 5).map((event: any) => ({
          id: event.id,
          subject: event.subject || 'Untitled Event',
          startTime: event.startTime,
          eventType: event.eventType,
          customerId: event.customerId,
        })),
      };
      
      setData(overviewData);
    } catch (error) {
      console.error('Error fetching calendar overview:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch calendar overview',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getEventTypeBadge = (eventType: string | null) => {
    const type = eventType || 'other';
    const colors = {
      sales_meeting: 'bg-green-100 text-green-800',
      support_call: 'bg-blue-100 text-blue-800',
      installation: 'bg-purple-100 text-purple-800',
      consultation: 'bg-orange-100 text-orange-800',
      follow_up: 'bg-yellow-100 text-yellow-800',
      maintenance: 'bg-red-100 text-red-800',
      training: 'bg-indigo-100 text-indigo-800',
      other: 'bg-gray-100 text-gray-800',
    };

    return (
      <Badge className={`${colors[type as keyof typeof colors] || colors.other} text-xs`}>
        {type.charAt(0).toUpperCase()}
      </Badge>
    );
  };

  const getSentimentIcon = (sentiment: number) => {
    if (sentiment > 0.2) return <TrendingUp className="w-4 h-4 text-green-600" />;
    if (sentiment < -0.2) return <TrendingDown className="w-4 h-4 text-red-600" />;
    return <div className="w-4 h-4 rounded-full bg-gray-400" />;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays < 7) return `In ${diffDays} days`;
    
    return date.toLocaleDateString('pl-PL', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading calendar overview...
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className={className}>
        <CardContent className="text-center p-6">
          <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600">No calendar data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Calendar Overview
            </CardTitle>
            <CardDescription>
              Upcoming events and AI insights
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm">
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{data.futureEvents}</div>
            <div className="text-sm text-gray-600">Upcoming</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Math.round(data.customerMatchRate * 100)}%
            </div>
            <div className="text-sm text-gray-600">Matched</div>
          </div>
        </div>

        {/* Quality Indicators */}
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              {getSentimentIcon(data.averageSentiment)}
              <span>Sentiment</span>
            </div>
            <span className="font-medium">
              {data.averageSentiment > 0 ? '+' : ''}{data.averageSentiment.toFixed(2)}
            </span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <Brain className="w-4 h-4 text-purple-600" />
              <span>AI Confidence</span>
            </div>
            <span className="font-medium">
              {Math.round(data.avgConfidence * 100)}%
            </span>
          </div>
        </div>

        {/* Top Event Types */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Event Types</h4>
          <div className="flex flex-wrap gap-1">
            {Object.entries(data.eventsByType)
              .sort(([, a], [, b]) => b - a)
              .slice(0, 4)
              .map(([type, count]) => (
                <Badge key={type} variant="outline" className="text-xs">
                  {type.replace('_', ' ')} ({count})
                </Badge>
              ))}
          </div>
        </div>

        {/* Next Events */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Next Events</h4>
          <div className="space-y-2">
            {data.nextEvents.length === 0 ? (
              <p className="text-sm text-gray-500">No upcoming events</p>
            ) : (
              data.nextEvents.map((event) => (
                <div key={event.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    {getEventTypeBadge(event.eventType)}
                    <span className="truncate">{event.subject}</span>
                    {event.customerId && (
                      <Brain className="w-3 h-3 text-purple-600 flex-shrink-0" />
                    )}
                  </div>
                  <span className="text-gray-500 text-xs flex-shrink-0 ml-2">
                    {formatDate(event.startTime)}
                  </span>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="pt-2 border-t">
          <div className="grid grid-cols-2 gap-2">
            <Button variant="outline" size="sm" className="text-xs">
              <Clock className="w-3 h-3 mr-1" />
              Sync Now
            </Button>
            <Button variant="outline" size="sm" className="text-xs">
              <Calendar className="w-3 h-3 mr-1" />
              View All
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
