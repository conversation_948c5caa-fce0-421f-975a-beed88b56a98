'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  Loader2,
  Play,
  Pause,
  Settings,
  Activity,
  Zap
} from 'lucide-react';
import { useToast } from '../ui/use-toast';

interface CalendarConnection {
  id: string;
  isActive: boolean;
  lastSyncAt: string | null;
  syncStatus: string;
  errorMessage: string | null;
  createdAt: string;
  scopes: string[];
  subscriptionId?: string;
  subscriptionExpiresAt?: string;
  lastWebhookAt?: string;
}

interface SyncLog {
  id: string;
  syncType: string;
  status: string;
  eventsProcessed: number;
  eventsCreated: number;
  eventsUpdated: number;
  eventsDeleted: number;
  errorMessage: string | null;
  durationMs: number | null;
  startedAt: string;
  completedAt: string | null;
}

export function CalendarSyncManager() {
  const [connections, setConnections] = useState<CalendarConnection[]>([]);
  const [syncLogs, setSyncLogs] = useState<SyncLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [autoSync, setAutoSync] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchConnectionStatus();
    fetchSyncLogs();
  }, []);

  const fetchConnectionStatus = async () => {
    try {
      const response = await fetch('/api/calendar/connect', {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch connection status');
      }

      const data = await response.json();
      setConnections(data.connections || []);
    } catch (error) {
      console.error('Error fetching connection status:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch calendar connection status',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSyncLogs = async () => {
    try {
      const response = await fetch('/api/calendar/sync/logs');
      
      if (!response.ok) {
        return; // Logs endpoint might not exist yet
      }

      const data = await response.json();
      setSyncLogs(data.logs || []);
    } catch (error) {
      console.error('Error fetching sync logs:', error);
    }
  };

  const triggerSync = async (syncType: 'full' | 'incremental' = 'incremental') => {
    try {
      setIsSyncing(true);
      
      const response = await fetch('/api/calendar/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ syncType }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to trigger sync');
      }

      const data = await response.json();
      
      toast({
        title: 'Sync Started',
        description: data.message,
      });

      // Refresh status after a delay
      setTimeout(() => {
        fetchConnectionStatus();
        fetchSyncLogs();
      }, 2000);

    } catch (error) {
      console.error('Error triggering sync:', error);
      toast({
        title: 'Sync Failed',
        description: 'Failed to trigger calendar sync',
        variant: 'destructive',
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const toggleConnection = async (connectionId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/calendar/connections/${connectionId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update connection');
      }

      await fetchConnectionStatus();
      
      toast({
        title: isActive ? 'Connection Enabled' : 'Connection Disabled',
        description: `Calendar connection has been ${isActive ? 'enabled' : 'disabled'}`,
      });

    } catch (error) {
      console.error('Error updating connection:', error);
      toast({
        title: 'Update Failed',
        description: 'Failed to update calendar connection',
        variant: 'destructive',
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      syncing: { color: 'bg-blue-100 text-blue-800', icon: RefreshCw },
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      error: { color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {status.toUpperCase()}
      </Badge>
    );
  };

  const formatDuration = (ms: number | null) => {
    if (!ms) return 'N/A';
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString('pl-PL');
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading sync status...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Sync Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <RefreshCw className="w-5 h-5 mr-2" />
                Calendar Sync Manager
              </CardTitle>
              <CardDescription>
                Manage calendar synchronization and monitor sync status
              </CardDescription>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label htmlFor="auto-sync" className="text-sm font-medium">
                  Auto Sync
                </label>
                <Switch
                  id="auto-sync"
                  checked={autoSync}
                  onCheckedChange={setAutoSync}
                />
              </div>
              <Button
                onClick={() => triggerSync('incremental')}
                disabled={isSyncing}
                variant="outline"
                size="sm"
              >
                {isSyncing ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4 mr-2" />
                )}
                Sync Now
              </Button>
              <Button
                onClick={() => triggerSync('full')}
                disabled={isSyncing}
                size="sm"
              >
                {isSyncing ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Zap className="w-4 h-4 mr-2" />
                )}
                Full Sync
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle>Connection Status</CardTitle>
          <CardDescription>
            Microsoft Graph calendar connections and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {connections.length === 0 ? (
            <div className="text-center py-6">
              <Settings className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600">No calendar connections found</p>
              <p className="text-sm text-gray-500 mt-2">
                Connect your Microsoft calendar to start syncing events
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {connections.map((connection) => (
                <div key={connection.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h4 className="font-medium">Microsoft Calendar</h4>
                        {getStatusBadge(connection.syncStatus)}
                        <Switch
                          checked={connection.isActive}
                          onCheckedChange={(checked) => toggleConnection(connection.id, checked)}
                        />
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Last Sync:</span>
                          <div>{formatDate(connection.lastSyncAt)}</div>
                        </div>
                        <div>
                          <span className="font-medium">Created:</span>
                          <div>{formatDate(connection.createdAt)}</div>
                        </div>
                        <div>
                          <span className="font-medium">Webhook:</span>
                          <div>
                            {connection.subscriptionId ? (
                              <Badge variant="outline" className="text-green-600">
                                <Activity className="w-3 h-3 mr-1" />
                                Active
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-gray-600">
                                Inactive
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div>
                          <span className="font-medium">Scopes:</span>
                          <div>{connection.scopes.length} permissions</div>
                        </div>
                      </div>
                      {connection.errorMessage && (
                        <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                          <AlertTriangle className="w-4 h-4 inline mr-1" />
                          {connection.errorMessage}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Sync Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Sync Activity</CardTitle>
          <CardDescription>
            History of calendar synchronization operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {syncLogs.length === 0 ? (
            <div className="text-center py-6">
              <Activity className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600">No sync activity yet</p>
            </div>
          ) : (
            <div className="space-y-3">
              {syncLogs.slice(0, 10).map((log) => (
                <div key={log.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(log.status)}
                      <span className="font-medium">{log.syncType.toUpperCase()} Sync</span>
                      <span className="text-sm text-gray-600">
                        {formatDate(log.startedAt)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      Duration: {formatDuration(log.durationMs)}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-4 gap-4 mt-2 text-sm">
                    <div>
                      <span className="text-gray-600">Processed:</span>
                      <span className="ml-1 font-medium">{log.eventsProcessed}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Created:</span>
                      <span className="ml-1 font-medium text-green-600">{log.eventsCreated}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Updated:</span>
                      <span className="ml-1 font-medium text-blue-600">{log.eventsUpdated}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Deleted:</span>
                      <span className="ml-1 font-medium text-red-600">{log.eventsDeleted}</span>
                    </div>
                  </div>

                  {log.errorMessage && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                      {log.errorMessage}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
