'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  Calendar, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Brain,
  Users,
  Target,
  BarChart3,
  Loader2,
  RefreshCw,
  Activity
} from 'lucide-react';
import { useToast } from '../ui/use-toast';

interface CustomerInsights {
  customerId: string;
  totalMeetings: number;
  lastMeetingDate: string | null;
  nextMeetingDate: string | null;
  meetingFrequencyDays: string;
  preferredMeetingTimes: Record<string, number>;
  commonMeetingTypes: string[];
  engagementScore: string;
  lastCalculatedAt: string;
}

interface CustomerCalendarInsightsProps {
  customerId: string;
  className?: string;
}

export function CustomerCalendarInsights({ customerId, className }: CustomerCalendarInsightsProps) {
  const [insights, setInsights] = useState<CustomerInsights | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchInsights();
  }, [customerId]);

  const fetchInsights = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/customers/${customerId}/calendar/insights`);
      
      if (!response.ok) {
        if (response.status === 404) {
          setInsights(null);
          return;
        }
        throw new Error('Failed to fetch customer calendar insights');
      }

      const data = await response.json();
      setInsights(data.insights);
    } catch (error) {
      console.error('Error fetching customer calendar insights:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch calendar insights',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshInsights = async () => {
    try {
      setIsRefreshing(true);
      
      // Trigger insights recalculation
      const response = await fetch(`/api/customers/${customerId}/calendar/insights`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error('Failed to refresh insights');
      }

      await fetchInsights();
      
      toast({
        title: 'Success',
        description: 'Calendar insights refreshed successfully',
      });
    } catch (error) {
      console.error('Error refreshing insights:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh calendar insights',
        variant: 'destructive',
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const getEngagementColor = (score: string) => {
    const numScore = parseFloat(score);
    if (numScore >= 0.8) return 'text-green-600';
    if (numScore >= 0.6) return 'text-yellow-600';
    if (numScore >= 0.4) return 'text-orange-600';
    return 'text-red-600';
  };

  const getEngagementIcon = (score: string) => {
    const numScore = parseFloat(score);
    if (numScore >= 0.7) return <TrendingUp className="w-4 h-4" />;
    if (numScore >= 0.4) return <Activity className="w-4 h-4" />;
    return <TrendingDown className="w-4 h-4" />;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('pl-PL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatFrequency = (days: string) => {
    const numDays = parseFloat(days);
    if (numDays === 0) return 'No pattern';
    if (numDays < 1) return 'Multiple times daily';
    if (numDays < 7) return `Every ${Math.round(numDays)} days`;
    if (numDays < 30) return `Every ${Math.round(numDays / 7)} weeks`;
    return `Every ${Math.round(numDays / 30)} months`;
  };

  const getPreferredTimeSlot = (times: Record<string, number>) => {
    if (Object.keys(times).length === 0) return 'No preference';
    
    const sortedTimes = Object.entries(times)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 2);
    
    return sortedTimes.map(([hour]) => {
      const h = parseInt(hour);
      if (h < 12) return `${h}:00 AM`;
      if (h === 12) return '12:00 PM';
      return `${h - 12}:00 PM`;
    }).join(', ');
  };

  const getEventTypeBadge = (type: string) => {
    const colors = {
      sales_meeting: 'bg-green-100 text-green-800',
      support_call: 'bg-blue-100 text-blue-800',
      installation: 'bg-purple-100 text-purple-800',
      consultation: 'bg-orange-100 text-orange-800',
      follow_up: 'bg-yellow-100 text-yellow-800',
      maintenance: 'bg-red-100 text-red-800',
      training: 'bg-indigo-100 text-indigo-800',
      other: 'bg-gray-100 text-gray-800',
    };

    return (
      <Badge className={colors[type as keyof typeof colors] || colors.other}>
        {type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading calendar insights...
        </CardContent>
      </Card>
    );
  }

  if (!insights) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Calendar Insights
          </CardTitle>
          <CardDescription>
            AI-powered analysis of customer calendar interactions
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center py-6">
          <Brain className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600 mb-4">No calendar insights available</p>
          <p className="text-sm text-gray-500">
            Calendar events will be analyzed automatically when available
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Calendar Insights
            </CardTitle>
            <CardDescription>
              AI-powered analysis of customer calendar interactions
            </CardDescription>
          </div>
          <Button
            onClick={refreshInsights}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
          >
            {isRefreshing ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{insights.totalMeetings}</div>
            <div className="text-sm text-gray-600">Total Meetings</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold flex items-center justify-center ${getEngagementColor(insights.engagementScore)}`}>
              {getEngagementIcon(insights.engagementScore)}
              <span className="ml-1">{Math.round(parseFloat(insights.engagementScore) * 100)}%</span>
            </div>
            <div className="text-sm text-gray-600">Engagement</div>
          </div>
        </div>

        {/* Meeting Timeline */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-700">Meeting Timeline</h4>
          <div className="grid grid-cols-1 gap-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Last Meeting:</span>
              <span className="font-medium">{formatDate(insights.lastMeetingDate)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Next Meeting:</span>
              <span className="font-medium">{formatDate(insights.nextMeetingDate)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Frequency:</span>
              <span className="font-medium">{formatFrequency(insights.meetingFrequencyDays)}</span>
            </div>
          </div>
        </div>

        {/* Preferences */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-700">Preferences</h4>
          <div className="space-y-2 text-sm">
            <div>
              <span className="text-gray-600">Preferred Times:</span>
              <div className="mt-1 font-medium">
                {getPreferredTimeSlot(insights.preferredMeetingTimes)}
              </div>
            </div>
            <div>
              <span className="text-gray-600">Common Meeting Types:</span>
              <div className="mt-1 flex flex-wrap gap-1">
                {insights.commonMeetingTypes.slice(0, 3).map((type) => (
                  <span key={type}>
                    {getEventTypeBadge(type)}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Last Updated */}
        <div className="pt-3 border-t text-xs text-gray-500">
          Last updated: {new Date(insights.lastCalculatedAt).toLocaleString('pl-PL')}
        </div>
      </CardContent>
    </Card>
  );
}
