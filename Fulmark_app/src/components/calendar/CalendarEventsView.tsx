'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Search, 
  Filter,
  TrendingUp, 
  TrendingDown,
  Brain,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { useToast } from '../ui/use-toast';

interface CalendarEvent {
  id: string;
  subject: string;
  bodyContent: string | null;
  startTime: string | null;
  endTime: string | null;
  location: string | null;
  attendees: any[];
  organizer: any;
  isAllDay: boolean;
  isCancelled: boolean;
  importance: 'low' | 'normal' | 'high';
  eventType: string | null;
  priority: number;
  confidenceScore: string;
  sentimentScore: string | null;
  aiAnalysis: any;
  actionItems: string[];
  createdAt: string;
  customerId?: string;
}

interface CalendarEventsViewProps {
  customerId?: string; // Optional - if provided, shows events for specific customer
  showFilters?: boolean;
  maxEvents?: number;
}

export function CalendarEventsView({ 
  customerId, 
  showFilters = true, 
  maxEvents = 50 
}: CalendarEventsViewProps) {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const { toast } = useToast();

  useEffect(() => {
    fetchEvents();
  }, [customerId]);

  const fetchEvents = async () => {
    try {
      setIsLoading(true);
      
      let url = '/api/calendar/events';
      const params = new URLSearchParams();
      
      if (customerId) {
        url = `/api/customers/${customerId}/calendar`;
      }
      
      if (maxEvents) {
        params.append('limit', maxEvents.toString());
      }
      
      if (dateFilter !== 'all') {
        const now = new Date();
        let startDate: Date;
        
        switch (dateFilter) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            params.append('startDate', startDate.toISOString());
            break;
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            params.append('startDate', startDate.toISOString());
            break;
          case 'month':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            params.append('startDate', startDate.toISOString());
            break;
          case 'future':
            params.append('startDate', now.toISOString());
            break;
        }
      }
      
      const queryString = params.toString();
      const response = await fetch(`${url}${queryString ? `?${queryString}` : ''}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch calendar events');
      }

      const data = await response.json();
      setEvents(data.events || []);
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch calendar events',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshEvents = async () => {
    setIsRefreshing(true);
    await fetchEvents();
    setIsRefreshing(false);
  };

  const getEventTypeBadge = (eventType: string | null) => {
    const type = eventType || 'other';
    const colors = {
      sales_meeting: 'bg-green-100 text-green-800',
      support_call: 'bg-blue-100 text-blue-800',
      installation: 'bg-purple-100 text-purple-800',
      consultation: 'bg-orange-100 text-orange-800',
      follow_up: 'bg-yellow-100 text-yellow-800',
      maintenance: 'bg-red-100 text-red-800',
      training: 'bg-indigo-100 text-indigo-800',
      other: 'bg-gray-100 text-gray-800',
    };

    return (
      <Badge className={colors[type as keyof typeof colors] || colors.other}>
        {type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const getSentimentIcon = (sentimentScore: string | null) => {
    if (!sentimentScore) return null;
    const score = parseFloat(sentimentScore);
    
    if (score > 0.2) {
      return <TrendingUp className="w-4 h-4 text-green-600" />;
    } else if (score < -0.2) {
      return <TrendingDown className="w-4 h-4 text-red-600" />;
    }
    return <div className="w-4 h-4 rounded-full bg-gray-400" />;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const formatTime = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Filter events based on search and filters
  const filteredEvents = events.filter(event => {
    const matchesSearch = !searchTerm || 
      event.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.bodyContent?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = eventTypeFilter === 'all' || event.eventType === eventTypeFilter;
    
    return matchesSearch && matchesType;
  });

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading calendar events...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Calendar Events</h2>
          <p className="text-gray-600">
            {customerId ? 'Customer calendar events' : 'All calendar events'} with AI insights
          </p>
        </div>
        <Button
          onClick={refreshEvents}
          disabled={isRefreshing}
          variant="outline"
          size="sm"
        >
          {isRefreshing ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          Refresh
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search events..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Event Type</label>
                <Select value={eventTypeFilter} onValueChange={setEventTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="sales_meeting">Sales Meeting</SelectItem>
                    <SelectItem value="support_call">Support Call</SelectItem>
                    <SelectItem value="installation">Installation</SelectItem>
                    <SelectItem value="consultation">Consultation</SelectItem>
                    <SelectItem value="follow_up">Follow Up</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                    <SelectItem value="training">Training</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Range</label>
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All dates" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Dates</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">Last 7 Days</SelectItem>
                    <SelectItem value="month">Last 30 Days</SelectItem>
                    <SelectItem value="future">Future Events</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Events List */}
      <Card>
        <CardHeader>
          <CardTitle>
            Events ({filteredEvents.length})
          </CardTitle>
          <CardDescription>
            AI-analyzed calendar events with customer insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredEvents.length === 0 ? (
            <div className="text-center py-6">
              <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600">
                {events.length === 0 ? 'No calendar events found' : 'No events match your filters'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredEvents.map((event) => (
                <div key={event.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium">{event.subject || 'Untitled Event'}</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {formatDate(event.startTime)}
                        </div>
                        {event.location && (
                          <div className="flex items-center">
                            <MapPin className="w-4 h-4 mr-1" />
                            {event.location}
                          </div>
                        )}
                        {event.attendees.length > 0 && (
                          <div className="flex items-center">
                            <Users className="w-4 h-4 mr-1" />
                            {event.attendees.length} attendees
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getSentimentIcon(event.sentimentScore)}
                      {getEventTypeBadge(event.eventType)}
                    </div>
                  </div>

                  {event.bodyContent && (
                    <div className="text-sm text-gray-700 bg-gray-50 rounded p-3">
                      {event.bodyContent.substring(0, 200)}
                      {event.bodyContent.length > 200 && '...'}
                    </div>
                  )}

                  {event.actionItems.length > 0 && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Action Items:</span>
                      <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                        {event.actionItems.slice(0, 3).map((item, index) => (
                          <li key={index}>{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-4">
                      <div>
                        Confidence: {Math.round(parseFloat(event.confidenceScore) * 100)}%
                      </div>
                      <div>
                        Priority: {event.priority}/5
                      </div>
                      {event.customerId && (
                        <div className="flex items-center">
                          <Brain className="w-3 h-3 mr-1" />
                          Customer Matched
                        </div>
                      )}
                    </div>
                    <div>
                      {formatTime(event.startTime)} - {formatTime(event.endTime)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
