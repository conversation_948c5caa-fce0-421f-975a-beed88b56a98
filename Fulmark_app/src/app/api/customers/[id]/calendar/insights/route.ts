import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '../../../../../../libs/db';
import { customerCalendarInsights, calendarEvents, calendarConnections } from '../../../../../../models/calendar';
import { eq, and, desc } from 'drizzle-orm';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Get customer calendar insights
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const customerId = params.id;

    // Get customer calendar insights
    const insights = await db
      .select()
      .from(customerCalendarInsights)
      .where(eq(customerCalendarInsights.customerId, customerId))
      .limit(1);

    if (!insights.length) {
      return NextResponse.json(
        { error: 'No calendar insights found for this customer' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      insights: insights[0],
    });

  } catch (error) {
    console.error('Customer calendar insights API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer calendar insights' },
      { status: 500 }
    );
  }
}

/**
 * Recalculate customer calendar insights
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const customerId = params.id;

    // Get user's calendar connections to verify access
    const userConnections = await db
      .select({ id: calendarConnections.id })
      .from(calendarConnections)
      .where(and(
        eq(calendarConnections.userId, userId),
        eq(calendarConnections.isActive, true)
      ));

    if (!userConnections.length) {
      return NextResponse.json(
        { error: 'No active calendar connections found' },
        { status: 404 }
      );
    }

    // Get all calendar events for this customer
    const customerEvents = await db
      .select()
      .from(calendarEvents)
      .where(eq(calendarEvents.customerId, customerId))
      .orderBy(desc(calendarEvents.startTime));

    if (customerEvents.length === 0) {
      return NextResponse.json(
        { error: 'No calendar events found for this customer' },
        { status: 404 }
      );
    }

    const now = new Date();
    const pastEvents = customerEvents.filter(event => 
      event.startTime && new Date(event.startTime) <= now
    );
    const futureEvents = customerEvents.filter(event => 
      event.startTime && new Date(event.startTime) > now
    );

    // 1. Calculate meeting frequency
    let meetingFrequencyDays = 0;
    if (pastEvents.length > 1) {
      const sortedPastEvents = pastEvents.sort((a, b) => 
        new Date(a.startTime!).getTime() - new Date(b.startTime!).getTime()
      );
      const firstEvent = new Date(sortedPastEvents[0].startTime!);
      const lastEvent = new Date(sortedPastEvents[sortedPastEvents.length - 1].startTime!);
      const daysDiff = (lastEvent.getTime() - firstEvent.getTime()) / (1000 * 60 * 60 * 24);
      meetingFrequencyDays = daysDiff / (pastEvents.length - 1);
    }

    // 2. Analyze preferred meeting times (hour of day)
    const hourCounts: Record<number, number> = {};
    customerEvents.forEach(event => {
      if (event.startTime) {
        const hour = new Date(event.startTime).getHours();
        hourCounts[hour] = (hourCounts[hour] || 0) + 1;
      }
    });

    // 3. Identify common meeting types
    const typeCounts: Record<string, number> = {};
    customerEvents.forEach(event => {
      const type = event.eventType || 'other';
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });
    const commonMeetingTypes = Object.entries(typeCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([type]) => type);

    // 4. Calculate engagement score based on multiple factors
    const avgConfidence = customerEvents.reduce((sum, event) => 
      sum + parseFloat(event.confidenceScore || '0'), 0) / customerEvents.length;
    
    const avgSentiment = customerEvents.reduce((sum, event) => 
      sum + parseFloat(event.sentimentScore || '0'), 0) / customerEvents.length;
    
    const recentActivity = pastEvents.filter(event => 
      event.startTime && new Date(event.startTime) > new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    ).length;
    
    const avgPriority = customerEvents.reduce((sum, event) => 
      sum + (event.priority || 3), 0) / customerEvents.length;

    // Engagement score formula (0.0 - 1.0)
    const engagementScore = Math.min(1.0, 
      (avgConfidence * 0.25) +                    // AI confidence in analysis
      ((avgSentiment + 1) / 2 * 0.25) +          // Sentiment (normalized to 0-1)
      (Math.min(recentActivity / 10, 1) * 0.3) + // Recent activity
      ((avgPriority / 5) * 0.2)                  // Average priority
    );

    // 5. Prepare insights data
    const insightsData = {
      customerId,
      totalMeetings: customerEvents.length,
      lastMeetingDate: pastEvents.length > 0 ? new Date(pastEvents[0].startTime!) : null,
      nextMeetingDate: futureEvents.length > 0 ? new Date(futureEvents[futureEvents.length - 1].startTime!) : null,
      meetingFrequencyDays: meetingFrequencyDays.toString(),
      preferredMeetingTimes: hourCounts,
      commonMeetingTypes,
      engagementScore: engagementScore.toString(),
      lastCalculatedAt: new Date(),
    };

    // 6. Upsert insights record
    const existingInsights = await db
      .select()
      .from(customerCalendarInsights)
      .where(eq(customerCalendarInsights.customerId, customerId))
      .limit(1);

    let updatedInsights;
    if (existingInsights.length > 0) {
      updatedInsights = await db
        .update(customerCalendarInsights)
        .set(insightsData)
        .where(eq(customerCalendarInsights.customerId, customerId))
        .returning();
    } else {
      updatedInsights = await db
        .insert(customerCalendarInsights)
        .values(insightsData)
        .returning();
    }

    return NextResponse.json({
      message: 'Customer calendar insights updated successfully',
      insights: updatedInsights[0],
      statistics: {
        totalEvents: customerEvents.length,
        pastEvents: pastEvents.length,
        futureEvents: futureEvents.length,
        engagementScore: Math.round(engagementScore * 100),
        avgConfidence: Math.round(avgConfidence * 100),
        avgSentiment: Math.round(avgSentiment * 100) / 100,
      },
    });

  } catch (error) {
    console.error('Customer calendar insights calculation API error:', error);
    return NextResponse.json(
      { error: 'Failed to calculate customer calendar insights' },
      { status: 500 }
    );
  }
}
