import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { microsoftGraphService, type WebhookNotification } from '../../../../services/microsoft-graph';
import { calendarSyncService } from '../../../../services/calendar-sync';
import { db } from '../../../../libs/db';
import { calendarConnections } from '../../../../models/calendar';
import { eq } from 'drizzle-orm';

/**
 * Handle Microsoft Graph webhook notifications for calendar changes
 * This endpoint receives real-time notifications when calendar events change
 */
export async function POST(request: NextRequest) {
  try {
    const headersList = headers();
    const validationToken = headersList.get('validationtoken');
    const clientState = headersList.get('clientstate');

    // Handle subscription validation
    if (validationToken) {
      console.log('Webhook validation request received');
      
      if (microsoftGraphService.validateWebhookNotification(validationToken, clientState)) {
        return new NextResponse(validationToken, {
          status: 200,
          headers: { 'Content-Type': 'text/plain' },
        });
      } else {
        console.error('Webhook validation failed');
        return NextResponse.json(
          { error: 'Validation failed' },
          { status: 400 }
        );
      }
    }

    // Handle actual webhook notifications
    const body = await request.json();
    const notifications: WebhookNotification[] = body.value || [];

    console.log(`Received ${notifications.length} webhook notifications`);

    // Process each notification
    for (const notification of notifications) {
      try {
        await processWebhookNotification(notification);
      } catch (error) {
        console.error('Error processing webhook notification:', error);
        // Continue processing other notifications
      }
    }

    return NextResponse.json({ message: 'Webhook processed successfully' });

  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Failed to process webhook' },
      { status: 500 }
    );
  }
}

/**
 * Process individual webhook notification
 */
async function processWebhookNotification(notification: WebhookNotification) {
  console.log(`Processing webhook notification: ${notification.changeType} for subscription ${notification.subscriptionId}`);

  try {
    // Find the calendar connection associated with this subscription
    const connection = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.subscriptionId, notification.subscriptionId))
      .limit(1);

    if (!connection.length) {
      console.warn(`No calendar connection found for subscription ${notification.subscriptionId}`);
      return;
    }

    const conn = connection[0];

    // Check if connection is still active
    if (!conn.isActive) {
      console.warn(`Calendar connection ${conn.id} is inactive, skipping webhook`);
      return;
    }

    // Handle different change types
    switch (notification.changeType) {
      case 'created':
      case 'updated':
        // Trigger incremental sync for this connection
        console.log(`Triggering incremental sync for connection ${conn.id} due to ${notification.changeType} event`);
        await calendarSyncService.syncCalendarEvents(conn.id, 'incremental');
        break;

      case 'deleted':
        // For deleted events, we might want to mark them as deleted in our database
        // For now, we'll trigger a sync to handle the deletion
        console.log(`Triggering incremental sync for connection ${conn.id} due to deleted event`);
        await calendarSyncService.syncCalendarEvents(conn.id, 'incremental');
        break;

      default:
        console.warn(`Unknown change type: ${notification.changeType}`);
    }

    // Update last webhook received timestamp
    await db
      .update(calendarConnections)
      .set({ 
        lastWebhookAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(calendarConnections.id, conn.id));

  } catch (error) {
    console.error(`Error processing webhook for subscription ${notification.subscriptionId}:`, error);
    throw error;
  }
}

/**
 * Handle GET requests (for testing/health check)
 */
export async function GET() {
  return NextResponse.json({
    message: 'Microsoft Graph Calendar Webhook Endpoint',
    status: 'active',
    timestamp: new Date().toISOString(),
  });
}
