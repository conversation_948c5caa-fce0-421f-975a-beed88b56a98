import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '../../../../../libs/db';
import { calendarConnections } from '../../../../../models/calendar';
import { eq, and } from 'drizzle-orm';
import { microsoftGraphService } from '../../../../../services/microsoft-graph';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Get specific calendar connection
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const connectionId = params.id;

    // Get the connection
    const connection = await db
      .select()
      .from(calendarConnections)
      .where(
        and(
          eq(calendarConnections.id, connectionId),
          eq(calendarConnections.userId, userId)
        )
      )
      .limit(1);

    if (!connection.length) {
      return NextResponse.json(
        { error: 'Calendar connection not found' },
        { status: 404 }
      );
    }

    // Remove sensitive data before returning
    const safeConnection = {
      ...connection[0],
      accessTokenEncrypted: undefined,
      refreshTokenEncrypted: undefined,
    };

    return NextResponse.json({
      connection: safeConnection,
    });

  } catch (error) {
    console.error('Calendar connection API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch calendar connection' },
      { status: 500 }
    );
  }
}

/**
 * Update calendar connection
 */
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const connectionId = params.id;
    const body = await request.json();
    const { isActive, errorMessage } = body;

    // Verify the connection belongs to the user
    const existingConnection = await db
      .select()
      .from(calendarConnections)
      .where(
        and(
          eq(calendarConnections.id, connectionId),
          eq(calendarConnections.userId, userId)
        )
      )
      .limit(1);

    if (!existingConnection.length) {
      return NextResponse.json(
        { error: 'Calendar connection not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (typeof isActive === 'boolean') {
      updateData.isActive = isActive;
      
      // If deactivating, also clear error message
      if (!isActive) {
        updateData.errorMessage = null;
        updateData.syncStatus = 'pending';
      }
    }

    if (errorMessage !== undefined) {
      updateData.errorMessage = errorMessage;
    }

    // Update the connection
    const updatedConnection = await db
      .update(calendarConnections)
      .set(updateData)
      .where(eq(calendarConnections.id, connectionId))
      .returning();

    // Remove sensitive data before returning
    const safeConnection = {
      ...updatedConnection[0],
      accessTokenEncrypted: undefined,
      refreshTokenEncrypted: undefined,
    };

    return NextResponse.json({
      message: 'Calendar connection updated successfully',
      connection: safeConnection,
    });

  } catch (error) {
    console.error('Calendar connection update API error:', error);
    return NextResponse.json(
      { error: 'Failed to update calendar connection' },
      { status: 500 }
    );
  }
}

/**
 * Delete calendar connection
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const connectionId = params.id;

    // Get the connection to check ownership and get subscription info
    const connection = await db
      .select()
      .from(calendarConnections)
      .where(
        and(
          eq(calendarConnections.id, connectionId),
          eq(calendarConnections.userId, userId)
        )
      )
      .limit(1);

    if (!connection.length) {
      return NextResponse.json(
        { error: 'Calendar connection not found' },
        { status: 404 }
      );
    }

    const conn = connection[0];

    // If there's a webhook subscription, try to delete it
    if (conn.subscriptionId && conn.accessTokenEncrypted) {
      try {
        const { tokenEncryptionService } = await import('../../../../../services/token-encryption');
        const accessToken = await tokenEncryptionService.decrypt(conn.accessTokenEncrypted);
        
        await microsoftGraphService.deleteCalendarSubscription(accessToken, conn.subscriptionId);
        console.log(`Deleted webhook subscription ${conn.subscriptionId}`);
      } catch (webhookError) {
        console.error('Failed to delete webhook subscription:', webhookError);
        // Continue with connection deletion even if webhook deletion fails
      }
    }

    // Delete the connection (this will cascade delete related sync logs and events)
    await db
      .delete(calendarConnections)
      .where(eq(calendarConnections.id, connectionId));

    return NextResponse.json({
      message: 'Calendar connection deleted successfully',
      connectionId,
    });

  } catch (error) {
    console.error('Calendar connection deletion API error:', error);
    return NextResponse.json(
      { error: 'Failed to delete calendar connection' },
      { status: 500 }
    );
  }
}

/**
 * Refresh calendar connection tokens
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const connectionId = params.id;

    // Get the connection
    const connection = await db
      .select()
      .from(calendarConnections)
      .where(
        and(
          eq(calendarConnections.id, connectionId),
          eq(calendarConnections.userId, userId)
        )
      )
      .limit(1);

    if (!connection.length) {
      return NextResponse.json(
        { error: 'Calendar connection not found' },
        { status: 404 }
      );
    }

    const conn = connection[0];

    if (!conn.refreshTokenEncrypted) {
      return NextResponse.json(
        { error: 'No refresh token available' },
        { status: 400 }
      );
    }

    // Refresh the tokens
    const { tokenEncryptionService } = await import('../../../../../services/token-encryption');
    const refreshToken = await tokenEncryptionService.decrypt(conn.refreshTokenEncrypted);
    const tokenData = await microsoftGraphService.refreshToken(refreshToken);

    // Encrypt new tokens
    const encryptedAccessToken = await tokenEncryptionService.encrypt(tokenData.accessToken);
    const encryptedRefreshToken = tokenData.refreshToken 
      ? await tokenEncryptionService.encrypt(tokenData.refreshToken)
      : conn.refreshTokenEncrypted;

    // Update connection with new tokens
    const updatedConnection = await db
      .update(calendarConnections)
      .set({
        accessTokenEncrypted: encryptedAccessToken,
        refreshTokenEncrypted: encryptedRefreshToken,
        expiresAt: tokenData.expiresOn || null,
        errorMessage: null,
        updatedAt: new Date(),
      })
      .where(eq(calendarConnections.id, connectionId))
      .returning();

    // Remove sensitive data before returning
    const safeConnection = {
      ...updatedConnection[0],
      accessTokenEncrypted: undefined,
      refreshTokenEncrypted: undefined,
    };

    return NextResponse.json({
      message: 'Calendar connection tokens refreshed successfully',
      connection: safeConnection,
    });

  } catch (error) {
    console.error('Calendar connection token refresh API error:', error);
    return NextResponse.json(
      { error: 'Failed to refresh calendar connection tokens' },
      { status: 500 }
    );
  }
}
