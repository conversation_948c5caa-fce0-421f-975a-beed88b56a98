import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '../../../../../libs/db';
import { calendarConnections, calendarSyncLogs } from '../../../../../models/calendar';
import { eq, desc, and } from 'drizzle-orm';

/**
 * Get calendar sync logs for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const searchParams = request.nextUrl.searchParams;
    const connectionId = searchParams.get('connectionId');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status'); // started, completed, failed
    const syncType = searchParams.get('syncType'); // full, incremental, webhook

    // Get user's calendar connections
    const userConnections = await db
      .select({ id: calendarConnections.id })
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId));

    if (!userConnections.length) {
      return NextResponse.json({
        logs: [],
        total: 0,
        message: 'No calendar connections found',
      });
    }

    const connectionIds = userConnections.map(conn => conn.id);

    // Build query conditions
    const conditions = [
      calendarSyncLogs.connectionId.in ? calendarSyncLogs.connectionId.in(connectionIds) : undefined
    ].filter(Boolean);

    // Add connection filter
    if (connectionId && connectionIds.includes(connectionId)) {
      conditions.push(eq(calendarSyncLogs.connectionId, connectionId));
    }

    // Add status filter
    if (status) {
      conditions.push(eq(calendarSyncLogs.status, status));
    }

    // Add sync type filter
    if (syncType) {
      conditions.push(eq(calendarSyncLogs.syncType, syncType));
    }

    // Get sync logs
    const logs = await db
      .select({
        id: calendarSyncLogs.id,
        connectionId: calendarSyncLogs.connectionId,
        syncType: calendarSyncLogs.syncType,
        status: calendarSyncLogs.status,
        eventsProcessed: calendarSyncLogs.eventsProcessed,
        eventsCreated: calendarSyncLogs.eventsCreated,
        eventsUpdated: calendarSyncLogs.eventsUpdated,
        eventsDeleted: calendarSyncLogs.eventsDeleted,
        errorMessage: calendarSyncLogs.errorMessage,
        durationMs: calendarSyncLogs.durationMs,
        startedAt: calendarSyncLogs.startedAt,
        completedAt: calendarSyncLogs.completedAt,
      })
      .from(calendarSyncLogs)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(calendarSyncLogs.startedAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await db
      .select({ count: calendarSyncLogs.id })
      .from(calendarSyncLogs)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const total = totalResult.length;

    // Calculate statistics
    const stats = {
      total,
      completed: logs.filter(log => log.status === 'completed').length,
      failed: logs.filter(log => log.status === 'failed').length,
      inProgress: logs.filter(log => log.status === 'started').length,
      totalEventsProcessed: logs.reduce((sum, log) => sum + (log.eventsProcessed || 0), 0),
      totalEventsCreated: logs.reduce((sum, log) => sum + (log.eventsCreated || 0), 0),
      totalEventsUpdated: logs.reduce((sum, log) => sum + (log.eventsUpdated || 0), 0),
      totalEventsDeleted: logs.reduce((sum, log) => sum + (log.eventsDeleted || 0), 0),
      averageDuration: logs.filter(log => log.durationMs).length > 0
        ? logs.filter(log => log.durationMs).reduce((sum, log) => sum + (log.durationMs || 0), 0) / logs.filter(log => log.durationMs).length
        : 0,
    };

    return NextResponse.json({
      logs,
      total,
      limit,
      offset,
      hasMore: offset + limit < total,
      statistics: stats,
    });

  } catch (error) {
    console.error('Calendar sync logs API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch calendar sync logs' },
      { status: 500 }
    );
  }
}

/**
 * Delete old sync logs (cleanup)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { olderThanDays = 30 } = body;

    // Get user's calendar connections
    const userConnections = await db
      .select({ id: calendarConnections.id })
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId));

    if (!userConnections.length) {
      return NextResponse.json({
        message: 'No calendar connections found',
        deleted: 0,
      });
    }

    const connectionIds = userConnections.map(conn => conn.id);

    // Calculate cutoff date
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    // Delete old logs
    const { lt } = await import('drizzle-orm');
    const deletedLogs = await db
      .delete(calendarSyncLogs)
      .where(
        and(
          calendarSyncLogs.connectionId.in ? calendarSyncLogs.connectionId.in(connectionIds) : undefined,
          lt(calendarSyncLogs.startedAt, cutoffDate)
        )
      )
      .returning({ id: calendarSyncLogs.id });

    return NextResponse.json({
      message: `Deleted ${deletedLogs.length} old sync logs`,
      deleted: deletedLogs.length,
      cutoffDate: cutoffDate.toISOString(),
    });

  } catch (error) {
    console.error('Calendar sync logs cleanup API error:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup sync logs' },
      { status: 500 }
    );
  }
}
