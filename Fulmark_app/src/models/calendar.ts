import { relations } from 'drizzle-orm';
import {
  boolean,
  decimal,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';

// Calendar connections table
export const calendarConnections = pgTable('calendar_connections', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: text('user_id').notNull(), // Clerk user ID
  microsoftTenantId: text('microsoft_tenant_id'),
  accessTokenEncrypted: text('access_token_encrypted'),
  refreshTokenEncrypted: text('refresh_token_encrypted'),
  expiresAt: timestamp('expires_at', { withTimezone: true }),
  scopes: text('scopes').array(),
  isActive: boolean('is_active').default(true),
  lastSyncAt: timestamp('last_sync_at', { withTimezone: true }),
  syncStatus: text('sync_status').default('pending'), // pending, syncing, completed, error
  errorMessage: text('error_message'),

  // Webhook subscription fields
  subscriptionId: text('subscription_id'), // Microsoft Graph subscription ID
  subscriptionExpiresAt: timestamp('subscription_expires_at', { withTimezone: true }),
  lastWebhookAt: timestamp('last_webhook_at', { withTimezone: true }),

  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

// Calendar events table
export const calendarEvents = pgTable('calendar_events', {
  id: uuid('id').primaryKey().defaultRandom(),
  microsoftEventId: text('microsoft_event_id').unique().notNull(),
  connectionId: uuid('connection_id').references(() => calendarConnections.id, { onDelete: 'cascade' }),
  customerId: uuid('customer_id'), // Will reference customers table
  subject: text('subject'),
  bodyContent: text('body_content'),
  bodyContentType: text('body_content_type').default('text'), // text, html
  startTime: timestamp('start_time', { withTimezone: true }),
  endTime: timestamp('end_time', { withTimezone: true }),
  location: text('location'),
  attendees: jsonb('attendees').default([]),
  organizer: jsonb('organizer'),
  isAllDay: boolean('is_all_day').default(false),
  isCancelled: boolean('is_cancelled').default(false),
  importance: text('importance').default('normal'), // low, normal, high
  sensitivity: text('sensitivity').default('normal'), // normal, personal, private, confidential
  showAs: text('show_as').default('busy'), // free, tentative, busy, oof, workingElsewhere, unknown
  
  // AI Analysis fields
  aiAnalysis: jsonb('ai_analysis').default({}),
  confidenceScore: decimal('confidence_score', { precision: 3, scale: 2 }).default('0.0'),
  eventType: text('event_type'), // sales_meeting, support_call, installation, consultation, follow_up
  priority: integer('priority').default(3), // 1-5 scale
  extractedCustomerInfo: jsonb('extracted_customer_info').default({}),
  sentimentScore: decimal('sentiment_score', { precision: 3, scale: 2 }), // -1.0 to 1.0
  actionItems: text('action_items').array(),
  
  // Metadata
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
  lastModified: timestamp('last_modified', { withTimezone: true }), // From Microsoft Graph
  changeKey: text('change_key'), // Microsoft Graph change tracking
});

// Calendar sync logs table
export const calendarSyncLogs = pgTable('calendar_sync_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  connectionId: uuid('connection_id').references(() => calendarConnections.id, { onDelete: 'cascade' }),
  syncType: text('sync_type').notNull(), // full, incremental, webhook
  status: text('status').notNull(), // started, completed, failed
  eventsProcessed: integer('events_processed').default(0),
  eventsCreated: integer('events_created').default(0),
  eventsUpdated: integer('events_updated').default(0),
  eventsDeleted: integer('events_deleted').default(0),
  errorMessage: text('error_message'),
  durationMs: integer('duration_ms'),
  startedAt: timestamp('started_at', { withTimezone: true }).defaultNow(),
  completedAt: timestamp('completed_at', { withTimezone: true }),
});

// Customer calendar insights table
export const customerCalendarInsights = pgTable('customer_calendar_insights', {
  id: uuid('id').primaryKey().defaultRandom(),
  customerId: uuid('customer_id'), // Will reference customers table
  totalMeetings: integer('total_meetings').default(0),
  lastMeetingDate: timestamp('last_meeting_date', { withTimezone: true }),
  nextMeetingDate: timestamp('next_meeting_date', { withTimezone: true }),
  meetingFrequencyDays: decimal('meeting_frequency_days', { precision: 5, scale: 2 }), // Average days between meetings
  preferredMeetingTimes: jsonb('preferred_meeting_times').default({}),
  commonMeetingTypes: text('common_meeting_types').array(),
  engagementScore: decimal('engagement_score', { precision: 3, scale: 2 }).default('0.0'), // 0.0 to 1.0
  lastCalculatedAt: timestamp('last_calculated_at', { withTimezone: true }).defaultNow(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

// Relations
export const calendarConnectionsRelations = relations(calendarConnections, ({ many }) => ({
  events: many(calendarEvents),
  syncLogs: many(calendarSyncLogs),
}));

export const calendarEventsRelations = relations(calendarEvents, ({ one }) => ({
  connection: one(calendarConnections, {
    fields: [calendarEvents.connectionId],
    references: [calendarConnections.id],
  }),
  // customer: one(customers, {
  //   fields: [calendarEvents.customerId],
  //   references: [customers.id],
  // }),
}));

export const calendarSyncLogsRelations = relations(calendarSyncLogs, ({ one }) => ({
  connection: one(calendarConnections, {
    fields: [calendarSyncLogs.connectionId],
    references: [calendarConnections.id],
  }),
}));

export const customerCalendarInsightsRelations = relations(customerCalendarInsights, ({ one }) => ({
  // customer: one(customers, {
  //   fields: [customerCalendarInsights.customerId],
  //   references: [customers.id],
  // }),
}));

// TypeScript types
export type CalendarConnection = typeof calendarConnections.$inferSelect;
export type NewCalendarConnection = typeof calendarConnections.$inferInsert;

export type CalendarEvent = typeof calendarEvents.$inferSelect;
export type NewCalendarEvent = typeof calendarEvents.$inferInsert;

export type CalendarSyncLog = typeof calendarSyncLogs.$inferSelect;
export type NewCalendarSyncLog = typeof calendarSyncLogs.$inferInsert;

export type CustomerCalendarInsights = typeof customerCalendarInsights.$inferSelect;
export type NewCustomerCalendarInsights = typeof customerCalendarInsights.$inferInsert;

// Enums for better type safety
export const SyncStatus = {
  PENDING: 'pending',
  SYNCING: 'syncing',
  COMPLETED: 'completed',
  ERROR: 'error',
} as const;

export const EventType = {
  SALES_MEETING: 'sales_meeting',
  SUPPORT_CALL: 'support_call',
  INSTALLATION: 'installation',
  CONSULTATION: 'consultation',
  FOLLOW_UP: 'follow_up',
  MAINTENANCE: 'maintenance',
  TRAINING: 'training',
  OTHER: 'other',
} as const;

export const SyncType = {
  FULL: 'full',
  INCREMENTAL: 'incremental',
  WEBHOOK: 'webhook',
} as const;

export const LogStatus = {
  STARTED: 'started',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

export type SyncStatusType = typeof SyncStatus[keyof typeof SyncStatus];
export type EventTypeType = typeof EventType[keyof typeof EventType];
export type SyncTypeType = typeof SyncType[keyof typeof SyncType];
export type LogStatusType = typeof LogStatus[keyof typeof LogStatus];
